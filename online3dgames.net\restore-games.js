const fs = require('fs');
const path = require('path');
const { completeGameData } = require('./game-config.js');

console.log('🔄 Restoring game pages from backups...\n');

// Get the latest backup files for each game
const backupDir = path.join(__dirname, 'backups');
const backupFiles = fs.readdirSync(backupDir);

// Group backups by timestamp (before the problematic update)
const gameBackups = {};

// Find the latest backup before the problematic update (07-51-40)
backupFiles.forEach(file => {
    if (file.includes('index.html') && file.includes('2025-08-01T07-51-40') && file.includes('506Z')) {
        // This is the blackjack backup - use this timestamp pattern for all games
        const timestamp = '2025-08-01T07-51-40';
        gameBackups['blackjack'] = file;
    }
});

// Map backup files to games based on the sequence
const gameKeys = Object.keys(completeGameData);
const backupSequence = [
    '502Z', '506Z', '510Z', '523Z', '527Z', '539Z', '543Z', '545Z', 
    '549Z', '552Z', '554Z', '556Z', '559Z', '561Z', '563Z', '564Z',
    '566Z', '568Z', '569Z', '571Z', '572Z', '575Z', '577Z', '578Z', '580Z'
];

gameKeys.forEach((gameKey, index) => {
    if (index < backupSequence.length) {
        const backupFile = `index.html.2025-08-01T07-51-40-${backupSequence[index]}.backup`;
        if (fs.existsSync(path.join(backupDir, backupFile))) {
            gameBackups[gameKey] = backupFile;
        }
    }
});

console.log('📋 Found backups for games:');
Object.keys(gameBackups).forEach(gameKey => {
    console.log(`   ${gameKey}: ${gameBackups[gameKey]}`);
});
console.log('');

// Restore each game
let restored = 0;
let failed = 0;

Object.keys(gameBackups).forEach(gameKey => {
    try {
        const game = completeGameData[gameKey];
        const gameDir = game.url.substring(1); // Remove leading slash
        const gamePath = path.join(__dirname, gameDir, 'index.html');
        const backupPath = path.join(backupDir, gameBackups[gameKey]);
        
        // Read backup content
        const backupContent = fs.readFileSync(backupPath, 'utf8');
        
        // Create a new backup of current broken file
        const brokenBackupPath = path.join(backupDir, `${gameDir}-broken-${Date.now()}.backup`);
        if (fs.existsSync(gamePath)) {
            fs.copyFileSync(gamePath, brokenBackupPath);
        }
        
        // Restore from backup
        fs.writeFileSync(gamePath, backupContent);
        
        console.log(`✅ Restored ${gameKey}`);
        restored++;
        
    } catch (error) {
        console.error(`❌ Failed to restore ${gameKey}: ${error.message}`);
        failed++;
    }
});

console.log('\n📊 Restoration Summary:');
console.log(`   ✅ Restored: ${restored}`);
console.log(`   ❌ Failed: ${failed}`);

if (restored > 0) {
    console.log('\n🎉 Game pages restored! Now running fixed update script...\n');
    
    // Run the fixed update script
    const { spawn } = require('child_process');
    const updateProcess = spawn('node', ['update-all-games.js'], { stdio: 'inherit' });
    
    updateProcess.on('close', (code) => {
        if (code === 0) {
            console.log('\n🎉 All games restored and updated successfully!');
        } else {
            console.log('\n⚠️ Update script failed. Games are restored but recommendations may need manual update.');
        }
    });
} else {
    console.log('\n❌ No games were restored. Please check backup files manually.');
}
