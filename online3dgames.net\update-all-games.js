#!/usr/bin/env node

// Batch Update Script for All Game Pages
// This script automatically updates all game pages with correct recommendations and SEO content

const fs = require('fs');
const path = require('path');

// Import configuration and template generator
const { gameCategories, completeGameData, generateHoneycombLayout, getGamesByCategory, getGamesExcludingCategory } = require('./game-config.js');
const { generateGameRecommendations, generateHomepageCategorySection, generateCategoryTabButton, generateSitemapEntries, validateHoneycombLayout } = require('./template-generator.js');

// Configuration
const BASE_DIR = __dirname;
const BACKUP_DIR = path.join(BASE_DIR, 'backups');

// Ensure backup directory exists
if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR);
}

// Create backup of a file
function createBackup(filePath) {
    if (fs.existsSync(filePath)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(BACKUP_DIR, `${path.basename(filePath)}.${timestamp}.backup`);
        fs.copyFileSync(filePath, backupPath);
        console.log(`✅ Backup created: ${backupPath}`);
    }
}

// Update game page recommendations
function updateGamePageRecommendations(gameKey) {
    const game = completeGameData[gameKey];
    if (!game) {
        console.error(`❌ Game not found: ${gameKey}`);
        return false;
    }
    
    // Determine the correct directory path
    const gameDir = game.url.substring(1); // Remove leading slash
    const indexPath = path.join(BASE_DIR, gameDir, 'index.html');
    
    if (!fs.existsSync(indexPath)) {
        console.error(`❌ Game page not found: ${indexPath}`);
        return false;
    }
    
    try {
        // Create backup
        createBackup(indexPath);
        
        // Read current content
        let content = fs.readFileSync(indexPath, 'utf8');
        
        // Find and replace recommendations section (try different comment formats)
        let recommendationsStart = content.indexOf('<!-- Game Recommendations -->');
        if (recommendationsStart === -1) {
            recommendationsStart = content.indexOf('<!-- Static Game Recommendations for SEO -->');
        }
        if (recommendationsStart === -1) {
            recommendationsStart = content.indexOf('<div class="game-recommendations');
        }

        // Find the end of recommendations section (look for script tags or end of body)
        let recommendationsEnd = content.indexOf('<script src="/assets/js/game-recommendations.js"></script>');
        if (recommendationsEnd === -1) {
            recommendationsEnd = content.indexOf('</body>');
        }
        if (recommendationsEnd === -1) {
            recommendationsEnd = content.lastIndexOf('</div>\n    </div>\n\n    <!-- Footer -->');
        }

        if (recommendationsStart === -1 || recommendationsEnd === -1) {
            console.error(`❌ Could not find recommendations section in ${gameKey}`);
            return false;
        }
        
        // Generate new recommendations
        const newRecommendations = generateGameRecommendations(gameKey);

        // Replace content - preserve game scripts
        const beforeRecommendations = content.substring(0, recommendationsStart);

        // Extract existing game scripts (before game-recommendations.js)
        const gameRecommendationsScriptPos = content.indexOf('<script src="/assets/js/game-recommendations.js"></script>');
        let gameScripts = '';

        if (gameRecommendationsScriptPos !== -1) {
            // Find all script tags before game-recommendations.js
            const beforeGameRecommendationsScript = content.substring(0, gameRecommendationsScriptPos);
            const lastRecommendationDiv = beforeGameRecommendationsScript.lastIndexOf('</div>');
            if (lastRecommendationDiv !== -1) {
                const scriptsSection = beforeGameRecommendationsScript.substring(lastRecommendationDiv + 6);
                const scriptMatches = scriptsSection.match(/<script[^>]*src="[^"]*"[^>]*><\/script>/g);
                if (scriptMatches) {
                    gameScripts = '\n    ' + scriptMatches.join('\n    ');
                }
            }
        }

        const afterRecommendations = gameScripts + '\n    <script src="/assets/js/game-recommendations.js"></script>\n</body>\n</html>';

        const newContent = beforeRecommendations + newRecommendations + afterRecommendations;
        
        // Write updated content
        fs.writeFileSync(indexPath, newContent, 'utf8');
        
        console.log(`✅ Updated recommendations for ${gameKey}`);
        return true;
        
    } catch (error) {
        console.error(`❌ Error updating ${gameKey}:`, error.message);
        return false;
    }
}

// Update homepage categories
function updateHomepageCategories() {
    const indexPath = path.join(BASE_DIR, 'index.html');
    
    if (!fs.existsSync(indexPath)) {
        console.error(`❌ Homepage not found: ${indexPath}`);
        return false;
    }
    
    try {
        // Create backup
        createBackup(indexPath);
        
        // Read current content
        let content = fs.readFileSync(indexPath, 'utf8');
        
        // Generate category tab buttons
        const categoryOrder = ['featured', 'card-games', 'blackjack', 'puzzle', 'arcade', 'relaxation', 'strategy'];
        let tabButtonsHtml = '';
        
        categoryOrder.forEach(categoryKey => {
            if (categoryKey === 'featured') {
                tabButtonsHtml += `                            <button class="tab-btn active" data-tab="featured" title="Featured Games - Our most popular and recommended games">
                                <span class="tab-icon">⭐</span>
                                <span class="tab-text">Featured</span>
                                <span class="tab-count">8</span>
                            </button>\n`;
            } else {
                const tabButton = generateCategoryTabButton(categoryKey);
                if (tabButton) {
                    tabButtonsHtml += tabButton + '\n';
                }
            }
        });
        
        // Generate category sections
        let categorySectionsHtml = '';
        categoryOrder.forEach(categoryKey => {
            if (categoryKey !== 'featured') {
                const categorySection = generateHomepageCategorySection(categoryKey);
                if (categorySection) {
                    categorySectionsHtml += '\n' + categorySection;
                }
            }
        });
        
        // Update tab buttons section
        const tabButtonsStart = content.indexOf('<div class="tab-buttons">');
        const tabButtonsEnd = content.indexOf('</div>', tabButtonsStart) + 6;
        
        if (tabButtonsStart !== -1 && tabButtonsEnd !== -1) {
            const beforeTabButtons = content.substring(0, tabButtonsStart);
            const afterTabButtons = content.substring(tabButtonsEnd);
            
            const newTabButtonsSection = `<div class="tab-buttons">
${tabButtonsHtml.trimEnd()}
                        </div>`;
            
            content = beforeTabButtons + newTabButtonsSection + afterTabButtons;
        }
        
        // Update category panels (after featured panel)
        const featuredPanelEnd = content.indexOf('</div>\n                        </div>\n                    </div>\n                </div>\n            </section>');

        if (featuredPanelEnd !== -1) {
            const beforeCategories = content.substring(0, featuredPanelEnd);
            const afterCategories = content.substring(featuredPanelEnd);

            content = beforeCategories + categorySectionsHtml + '\n                        ' + afterCategories;
        }

        // Update static honeycomb layout for SEO
        const honeycombStart = content.indexOf('<!-- Static game cards for SEO - Honeycomb Row 1 -->');
        const honeycombEnd = content.lastIndexOf('</a>\n                        </div>\n                    </div>');

        if (honeycombStart !== -1 && honeycombEnd !== -1) {
            // Get all games for honeycomb layout with keys
            const allGamesWithKeys = Object.entries(completeGameData).map(([key, game]) => ({
                ...game,
                key: key
            }));
            const honeycombLayout = generateHoneycombLayout(allGamesWithKeys);

            let honeycombHtml = '';
            honeycombLayout.forEach((row, index) => {
                const offsetClass = row.isOffset ? ' offset' : '';
                honeycombHtml += `                        <!-- Static game cards for SEO - Honeycomb Row ${index + 1} -->\n`;
                honeycombHtml += `                        <div class="honeycomb-row${offsetClass}">\n`;

                row.games.forEach(game => {
                    honeycombHtml += `                            <a href="${game.url}" class="game-card" data-game="${game.key}" data-categories="${game.featured ? 'featured,' : ''}${game.category}" title="${game.seoTitle}">\n`;
                    honeycombHtml += `                                <div class="card-image">\n`;
                    honeycombHtml += `                                    <div class="image-bg ${game.key}-bg"></div>\n`;
                    honeycombHtml += `                                    <div class="game-icon">${game.icon}</div>\n`;
                    honeycombHtml += `                                </div>\n`;
                    honeycombHtml += `                                <div class="card-info">\n`;
                    honeycombHtml += `                                    <h3 class="game-name">${game.name}</h3>\n`;
                    honeycombHtml += `                                    <p class="game-description">${game.description}</p>\n`;
                    honeycombHtml += `                                    <div class="game-tags">\n`;
                    game.tags.forEach(tag => {
                        honeycombHtml += `                                        <span class="tag">${tag}</span>\n`;
                    });
                    honeycombHtml += `                                    </div>\n`;
                    honeycombHtml += `                                    <div class="game-meta">\n`;
                    honeycombHtml += `                                        <div class="rating">\n`;
                    honeycombHtml += `                                            <span class="stars">${'⭐'.repeat(Math.floor(game.rating))}</span>\n`;
                    honeycombHtml += `                                            <span class="rating-text">${game.rating}</span>\n`;
                    honeycombHtml += `                                        </div>\n`;
                    honeycombHtml += `                                        <div class="difficulty">${'⭐'.repeat(game.difficulty)}</div>\n`;
                    honeycombHtml += `                                    </div>\n`;
                    honeycombHtml += `                                </div>\n`;
                    honeycombHtml += `                            </a>\n`;
                });

                honeycombHtml += `                        </div>\n`;
                if (index < honeycombLayout.length - 1) {
                    honeycombHtml += `                        \n`;
                }
            });

            const beforeHoneycomb = content.substring(0, honeycombStart);
            // Find the end of the honeycomb section more precisely to preserve the rest of the page
            const honeycombSectionEnd = content.indexOf('</div>\n        </div>\n    </div>\n\n    <!-- Scripts -->', honeycombEnd);

            if (honeycombSectionEnd !== -1) {
                // Preserve everything after the honeycomb section including scripts
                const afterHoneycomb = content.substring(honeycombSectionEnd);
                content = beforeHoneycomb + honeycombHtml.trimEnd() + '\n                    </div>\n        </div>\n    </div>\n\n    <!-- Scripts -->' + afterHoneycomb.substring(afterHoneycomb.indexOf('<script'));
            } else {
                // Fallback: preserve everything after the last game card
                const afterHoneycomb = content.substring(honeycombEnd + '</a>\n                        </div>\n                    </div>'.length);
                content = beforeHoneycomb + honeycombHtml.trimEnd() + '\n                    </div>' + afterHoneycomb;
            }
        }

        // Write updated content
        fs.writeFileSync(indexPath, content, 'utf8');
        
        console.log(`✅ Updated homepage categories`);
        return true;
        
    } catch (error) {
        console.error(`❌ Error updating homepage:`, error.message);
        return false;
    }
}

// Update sitemap.xml
function updateSitemap() {
    const sitemapPath = path.join(BASE_DIR, 'sitemap.xml');
    
    try {
        // Create backup
        createBackup(sitemapPath);
        
        // Generate sitemap entries
        const gameEntries = generateSitemapEntries();
        
        const sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://online3dgames.net</loc>
        <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
${gameEntries.trimEnd()}
</urlset>`;
        
        // Write sitemap
        fs.writeFileSync(sitemapPath, sitemapContent, 'utf8');
        
        console.log(`✅ Updated sitemap.xml`);
        return true;
        
    } catch (error) {
        console.error(`❌ Error updating sitemap:`, error.message);
        return false;
    }
}

// Validate all honeycomb layouts
function validateAllLayouts() {
    console.log('\n🔍 Validating honeycomb layouts...\n');
    
    let allValid = true;
    
    Object.keys(completeGameData).forEach(gameKey => {
        const game = completeGameData[gameKey];
        const sameCategory = getGamesByCategory(game.category).filter(g => g.url !== game.url);
        const otherGames = getGamesExcludingCategory(game.category).slice(0, 12);
        
        // Validate similar games layout
        if (sameCategory.length > 0) {
            const similarValidation = validateHoneycombLayout(sameCategory);
            if (!similarValidation.isValid) {
                console.log(`❌ ${gameKey} similar games layout issues:`);
                similarValidation.errors.forEach(error => console.log(`   ${error}`));
                allValid = false;
            }
        }
        
        // Validate other games layout
        const otherValidation = validateHoneycombLayout(otherGames);
        if (!otherValidation.isValid) {
            console.log(`❌ ${gameKey} other games layout issues:`);
            otherValidation.errors.forEach(error => console.log(`   ${error}`));
            allValid = false;
        }
    });
    
    if (allValid) {
        console.log('✅ All honeycomb layouts are valid!\n');
    } else {
        console.log('❌ Some layouts need fixing\n');
    }
    
    return allValid;
}

// Main execution function
function main() {
    console.log('🚀 Starting batch update of all game pages...\n');
    
    let successCount = 0;
    let totalCount = 0;
    
    // Update all game pages
    console.log('📝 Updating game page recommendations...\n');
    Object.keys(completeGameData).forEach(gameKey => {
        totalCount++;
        if (updateGamePageRecommendations(gameKey)) {
            successCount++;
        }
    });
    
    // Update homepage
    console.log('\n🏠 Updating homepage...\n');
    if (updateHomepageCategories()) {
        successCount++;
    }
    totalCount++;
    
    // Update sitemap
    console.log('\n🗺️ Updating sitemap...\n');
    if (updateSitemap()) {
        successCount++;
    }
    totalCount++;
    
    // Validate layouts
    validateAllLayouts();
    
    // Summary
    console.log(`\n📊 Update Summary:`);
    console.log(`   ✅ Successful: ${successCount}/${totalCount}`);
    console.log(`   ❌ Failed: ${totalCount - successCount}/${totalCount}`);
    
    if (successCount === totalCount) {
        console.log('\n🎉 All updates completed successfully!');
    } else {
        console.log('\n⚠️ Some updates failed. Check the logs above.');
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    updateGamePageRecommendations,
    updateHomepageCategories,
    updateSitemap,
    validateAllLayouts,
    main
};
