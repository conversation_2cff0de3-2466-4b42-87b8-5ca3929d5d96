#!/usr/bin/env node

// Test Configuration Script
// This script validates the game configuration and checks for any issues before running the full update

const fs = require('fs');
const path = require('path');

try {
    // Import configuration
    const { gameCategories, completeGameData, generateHoneycombLayout, getGamesByCategory, getGamesExcludingCategory } = require('./game-config.js');
    const { validateHoneycombLayout } = require('./template-generator.js');

    console.log('🔍 Testing Game Configuration...\n');

    // Test 1: Check if all games have required properties
    console.log('📋 Checking game data completeness...');
    let gameDataValid = true;
    const requiredProps = ['name', 'icon', 'rating', 'difficulty', 'description', 'tags', 'url', 'bgClass', 'category', 'seoTitle', 'seoDescription'];

    Object.keys(completeGameData).forEach(gameKey => {
        const game = completeGameData[gameKey];
        const missingProps = requiredProps.filter(prop => !game[prop]);
        
        if (missingProps.length > 0) {
            console.log(`❌ ${gameKey} missing properties: ${missingProps.join(', ')}`);
            gameDataValid = false;
        }
    });

    if (gameDataValid) {
        console.log('✅ All games have complete data\n');
    } else {
        console.log('❌ Some games have missing data\n');
    }

    // Test 2: Check if all game directories exist and URL paths match
    console.log('📁 Checking game directories and URL consistency...');
    let directoriesValid = true;

    Object.values(completeGameData).forEach(game => {
        const gameDir = game.url.substring(1); // Remove leading slash
        const gamePath = path.join(__dirname, gameDir);
        const indexPath = path.join(gamePath, 'index.html');

        if (!fs.existsSync(gamePath)) {
            console.log(`❌ Directory missing: ${gameDir} (URL: ${game.url})`);
            directoriesValid = false;
        } else if (!fs.existsSync(indexPath)) {
            console.log(`❌ index.html missing: ${gameDir}/index.html`);
            directoriesValid = false;
        } else {
            // Verify URL path matches directory name
            const expectedUrl = `/${gameDir}`;
            if (game.url !== expectedUrl) {
                console.log(`❌ URL mismatch: ${game.name} - URL: ${game.url}, Directory: ${gameDir}`);
                directoriesValid = false;
            }
        }
    });

    if (directoriesValid) {
        console.log('✅ All game directories, index.html files exist, and URL paths match directories\n');
    } else {
        console.log('❌ Some game directories, files are missing, or URL paths don\'t match\n');
    }

    // Test 3: Check category consistency
    console.log('🏷️ Checking category consistency...');
    let categoriesValid = true;

    Object.keys(gameCategories).forEach(categoryKey => {
        const category = gameCategories[categoryKey];
        const gamesInCategory = getGamesByCategory(categoryKey);
        const expectedCount = category.games ? category.games.length : 0;
        
        if (gamesInCategory.length !== expectedCount) {
            console.log(`❌ Category ${categoryKey}: expected ${expectedCount} games, found ${gamesInCategory.length}`);
            categoriesValid = false;
        }
        
        // Check if all games in category exist in completeGameData
        if (category.games) {
            category.games.forEach(gameKey => {
                if (!completeGameData[gameKey]) {
                    console.log(`❌ Game ${gameKey} in category ${categoryKey} not found in completeGameData`);
                    categoriesValid = false;
                }
            });
        }
    });

    if (categoriesValid) {
        console.log('✅ All categories are consistent\n');
    } else {
        console.log('❌ Some category inconsistencies found\n');
    }

    // Test 4: Validate honeycomb layouts
    console.log('🔷 Validating honeycomb layouts...');
    let layoutsValid = true;

    Object.keys(completeGameData).forEach(gameKey => {
        const game = completeGameData[gameKey];
        const sameCategory = getGamesByCategory(game.category).filter(g => g.url !== game.url);
        const otherGames = getGamesExcludingCategory(game.category).slice(0, 12);
        
        // Validate similar games layout
        if (sameCategory.length > 0) {
            const similarValidation = validateHoneycombLayout(sameCategory);
            if (!similarValidation.isValid) {
                console.log(`❌ ${gameKey} similar games layout issues:`);
                similarValidation.errors.forEach(error => console.log(`   ${error}`));
                layoutsValid = false;
            }
        }
        
        // Validate other games layout
        const otherValidation = validateHoneycombLayout(otherGames);
        if (!otherValidation.isValid) {
            console.log(`❌ ${gameKey} other games layout issues:`);
            otherValidation.errors.forEach(error => console.log(`   ${error}`));
            layoutsValid = false;
        }
    });

    if (layoutsValid) {
        console.log('✅ All honeycomb layouts are valid\n');
    } else {
        console.log('❌ Some layout issues found\n');
    }

    // Test 5: Check for duplicate URLs
    console.log('🔗 Checking for duplicate URLs...');
    const urls = Object.values(completeGameData).map(game => game.url);
    const duplicateUrls = urls.filter((url, index) => urls.indexOf(url) !== index);
    
    if (duplicateUrls.length === 0) {
        console.log('✅ No duplicate URLs found\n');
    } else {
        console.log(`❌ Duplicate URLs found: ${duplicateUrls.join(', ')}\n`);
    }

    // Summary
    const allValid = gameDataValid && directoriesValid && categoriesValid && layoutsValid && duplicateUrls.length === 0;
    
    console.log('📊 Test Summary:');
    console.log(`   Game Data: ${gameDataValid ? '✅' : '❌'}`);
    console.log(`   Directories: ${directoriesValid ? '✅' : '❌'}`);
    console.log(`   Categories: ${categoriesValid ? '✅' : '❌'}`);
    console.log(`   Layouts: ${layoutsValid ? '✅' : '❌'}`);
    console.log(`   URLs: ${duplicateUrls.length === 0 ? '✅' : '❌'}`);
    
    if (allValid) {
        console.log('\n🎉 All tests passed! Ready to run batch update.');
        console.log('\n💡 To run the batch update:');
        console.log('   node update-all-games.js');
    } else {
        console.log('\n⚠️ Some tests failed. Please fix the issues before running batch update.');
    }

    // Show game counts by category
    console.log('\n📈 Game counts by category:');
    Object.keys(gameCategories).forEach(categoryKey => {
        const count = getGamesByCategory(categoryKey).length;
        console.log(`   ${gameCategories[categoryKey].name}: ${count} games`);
    });

    console.log(`\n📊 Total games: ${Object.keys(completeGameData).length}`);

    // Test 6: Check recommendation lists for each game
    console.log('\n🎯 Checking recommendation lists for each game...');
    let recommendationsValid = true;

    Object.keys(completeGameData).forEach(gameKey => {
        const game = completeGameData[gameKey];
        const sameCategory = getGamesByCategory(game.category).filter(g => g.url !== game.url);
        const otherGames = getGamesExcludingCategory(game.category);

        console.log(`\n🎮 ${gameKey} (${game.category}):`);
        console.log(`   Same category games: ${sameCategory.length} (${sameCategory.map(g => g.name).join(', ')})`);
        console.log(`   Other games available: ${otherGames.length}`);

        if (sameCategory.length === 0) {
            console.log(`   ⚠️ No same-category games for recommendations`);
        }

        if (otherGames.length === 0) {
            console.log(`   ❌ No other games for recommendations`);
            recommendationsValid = false;
        }

        // Check if honeycomb layout will work
        if (sameCategory.length > 0) {
            const similarLayout = generateHoneycombLayout(sameCategory);
            console.log(`   Similar games layout: ${similarLayout.map(row => row.games.length).join('-')} pattern`);
        }

        const otherLayout = generateHoneycombLayout(otherGames.slice(0, 12));
        console.log(`   Other games layout: ${otherLayout.map(row => row.games.length).join('-')} pattern`);
    });

    if (recommendationsValid) {
        console.log('\n✅ All games have valid recommendation lists');
    } else {
        console.log('\n❌ Some games have insufficient recommendations');
    }

    console.log(`\n📊 Total games: ${Object.keys(completeGameData).length}`);

} catch (error) {
    console.error('❌ Error testing configuration:', error.message);
    console.error('\n💡 Make sure you have Node.js installed and all files are in the correct location.');
}
