const fs = require('fs');
const path = require('path');
const { completeGameData } = require('./game-config.js');

console.log('🔄 Restoring game pages from backups (without update)...\n');

// Get the latest backup files for each game
const backupDir = path.join(__dirname, 'backups');

// Map backup files to games based on the sequence
const gameKeys = Object.keys(completeGameData);
const backupSequence = [
    '502Z', '506Z', '510Z', '523Z', '527Z', '539Z', '543Z', '545Z', 
    '549Z', '552Z', '554Z', '556Z', '559Z', '561Z', '563Z', '564Z',
    '566Z', '568Z', '569Z', '571Z', '572Z', '575Z', '577Z', '578Z', '580Z'
];

const gameBackups = {};
gameKeys.forEach((gameKey, index) => {
    if (index < backupSequence.length) {
        const backupFile = `index.html.2025-08-01T07-51-40-${backupSequence[index]}.backup`;
        if (fs.existsSync(path.join(backupDir, backupFile))) {
            gameBackups[gameKey] = backupFile;
        }
    }
});

console.log('📋 Found backups for games:');
Object.keys(gameBackups).forEach(gameKey => {
    console.log(`   ${gameKey}: ${gameBackups[gameKey]}`);
});
console.log('');

// Restore each game
let restored = 0;
let failed = 0;

Object.keys(gameBackups).forEach(gameKey => {
    try {
        const game = completeGameData[gameKey];
        const gameDir = game.url.substring(1); // Remove leading slash
        const gamePath = path.join(__dirname, gameDir, 'index.html');
        const backupPath = path.join(backupDir, gameBackups[gameKey]);
        
        // Read backup content
        const backupContent = fs.readFileSync(backupPath, 'utf8');
        
        // Restore from backup
        fs.writeFileSync(gamePath, backupContent);
        
        console.log(`✅ Restored ${gameKey}`);
        restored++;
        
    } catch (error) {
        console.error(`❌ Failed to restore ${gameKey}: ${error.message}`);
        failed++;
    }
});

console.log('\n📊 Restoration Summary:');
console.log(`   ✅ Restored: ${restored}`);
console.log(`   ❌ Failed: ${failed}`);

if (restored > 0) {
    console.log('\n🎉 All game pages restored successfully!');
    console.log('💡 Games should now work normally.');
    console.log('⚠️  Note: Recommendations may show old content until you run the fixed update script.');
} else {
    console.log('\n❌ No games were restored. Please check backup files manually.');
}
